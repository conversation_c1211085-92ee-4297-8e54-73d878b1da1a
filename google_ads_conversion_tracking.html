<!DOCTYPE html>
<html>
<head>
    <title>Google Ads Conversion Tracking для Admins.Today</title>
</head>
<body>

<!-- 1. ГЛОБАЛЬНЫЙ ТЕГ GOOGLE ADS (разместить в <head> всех страниц) -->
<script async src="https://www.googletagmanager.com/gtag/js?id=AW-XXXXXXXXXX"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'AW-XXXXXXXXXX'); // Заменить на ваш ID
</script>

<!-- 2. ФУНКЦИЯ ДЛЯ ОТСЛЕЖИВАНИЯ КОНВЕРСИЙ -->
<script>
  // Универсальная функция для отправки событий конверсии
  function gtagSendEvent(eventName, value, currency, url) {
    var callback = function () {
      if (typeof url === 'string') {
        window.location = url;
      }
    };
    
    gtag('event', 'conversion', {
      'send_to': 'AW-XXXXXXXXXX/' + eventName, // Заменить на ваш ID
      'value': value || 0,
      'currency': currency || 'USD',
      'event_callback': callback,
      'event_timeout': 2000
    });
    return false;
  }

  // Специфические функции для разных типов конверсий
  
  // 1. Заявка через форму (основная конверсия)
  function trackFormSubmission() {
    gtag('event', 'conversion', {
      'send_to': 'AW-XXXXXXXXXX/FORM_CONVERSION_LABEL',
      'value': 100.0,
      'currency': 'USD',
      'transaction_id': 'form_' + Date.now()
    });
  }

  // 2. Звонок с сайта
  function trackPhoneCall() {
    gtag('event', 'conversion', {
      'send_to': 'AW-XXXXXXXXXX/PHONE_CONVERSION_LABEL',
      'value': 80.0,
      'currency': 'USD'
    });
  }

  // 3. Переход на страницу контактов
  function trackContactPageVisit() {
    gtag('event', 'conversion', {
      'send_to': 'AW-XXXXXXXXXX/CONTACT_CONVERSION_LABEL',
      'value': 20.0,
      'currency': 'USD'
    });
  }

  // 4. Клик по кнопке "Заказать услуги"
  function trackOrderButtonClick() {
    gtag('event', 'conversion', {
      'send_to': 'AW-XXXXXXXXXX/ORDER_CONVERSION_LABEL',
      'value': 50.0,
      'currency': 'USD'
    });
  }

  // 5. Выбор тарифа
  function trackPricingSelection(plan, price) {
    gtag('event', 'conversion', {
      'send_to': 'AW-XXXXXXXXXX/PRICING_CONVERSION_LABEL',
      'value': price,
      'currency': 'USD',
      'custom_parameters': {
        'plan_name': plan
      }
    });
  }
</script>

<!-- 3. ПРИМЕРЫ ИНТЕГРАЦИИ В HTML -->

<!-- Форма заказа услуг -->
<form id="orderForm" onsubmit="trackFormSubmission(); return true;">
  <input type="text" name="name" placeholder="Ваше имя" required>
  <input type="email" name="email" placeholder="Email" required>
  <select name="service">
    <option value="techsupport">Техподдержка</option>
    <option value="sysadmin">Системное администрирование</option>
    <option value="cloud">Облачные решения</option>
    <option value="security">Безопасность</option>
  </select>
  <textarea name="description" placeholder="Описание задачи"></textarea>
  <button type="submit">Заказать услуги</button>
</form>

<!-- Кнопки выбора тарифов -->
<div class="pricing-buttons">
  <button onclick="trackPricingSelection('Базовый', 20)">Базовый - $20/месяц</button>
  <button onclick="trackPricingSelection('Стандарт', 50)">Стандарт - $50/месяц</button>
  <button onclick="trackPricingSelection('Премиум', 100)">Премиум - $100/месяц</button>
  <button onclick="trackPricingSelection('Корпоративный', 200)">Корпоративный - $200/месяц</button>
</div>

<!-- Ссылка на контакты -->
<a href="/contacts" onclick="trackContactPageVisit()">Контакты</a>

<!-- Телефонная ссылка -->
<a href="tel:+380123456789" onclick="trackPhoneCall()">Позвонить</a>

<!-- Кнопка заказа услуг -->
<button onclick="trackOrderButtonClick()">Заказать услуги</button>

<!-- 4. ОТСЛЕЖИВАНИЕ МИКРОКОНВЕРСИЙ -->
<script>
  // Время на сайте больше 2 минут
  setTimeout(function() {
    gtag('event', 'engagement', {
      'send_to': 'AW-XXXXXXXXXX/ENGAGEMENT_LABEL',
      'value': 5.0,
      'currency': 'USD'
    });
  }, 120000); // 2 минуты

  // Просмотр более 3 страниц
  var pageViews = parseInt(sessionStorage.getItem('pageViews') || '0') + 1;
  sessionStorage.setItem('pageViews', pageViews);
  
  if (pageViews >= 3) {
    gtag('event', 'page_view_milestone', {
      'send_to': 'AW-XXXXXXXXXX/PAGEVIEW_LABEL',
      'value': 10.0,
      'currency': 'USD'
    });
  }

  // Скролл до конца страницы
  window.addEventListener('scroll', function() {
    if ((window.innerHeight + window.scrollY) >= document.body.offsetHeight - 100) {
      gtag('event', 'scroll_to_bottom', {
        'send_to': 'AW-XXXXXXXXXX/SCROLL_LABEL',
        'value': 5.0,
        'currency': 'USD'
      });
    }
  });
</script>

<!-- 5. ENHANCED CONVERSIONS (улучшенные конверсии) -->
<script>
  function trackEnhancedConversion(email, phone, firstName, lastName) {
    gtag('config', 'AW-XXXXXXXXXX', {
      'user_data': {
        'email_address': email,
        'phone_number': phone,
        'first_name': firstName,
        'last_name': lastName
      }
    });
  }

  // Вызывать при отправке формы с данными пользователя
  function submitFormWithEnhancedTracking() {
    var email = document.getElementById('email').value;
    var phone = document.getElementById('phone').value;
    var name = document.getElementById('name').value.split(' ');
    
    trackEnhancedConversion(
      email,
      phone,
      name[0] || '',
      name[1] || ''
    );
    
    trackFormSubmission();
  }
</script>

<!-- 6. ОТСЛЕЖИВАНИЕ ОПЛАТЫ (для WayForPay) -->
<script>
  // Функция для отслеживания успешной оплаты
  function trackPaymentSuccess(amount, currency, transactionId) {
    gtag('event', 'purchase', {
      'send_to': 'AW-XXXXXXXXXX/PURCHASE_LABEL',
      'value': amount,
      'currency': currency,
      'transaction_id': transactionId,
      'items': [{
        'item_id': 'admin_service',
        'item_name': 'Услуги администрирования',
        'category': 'IT Services',
        'quantity': 1,
        'price': amount
      }]
    });
  }

  // Интеграция с WayForPay
  function wayForPaySuccess(response) {
    trackPaymentSuccess(
      response.amount,
      response.currency,
      response.orderReference
    );
  }
</script>

<!-- 7. ОТСЛЕЖИВАНИЕ КЛИКОВ ПО ВНЕШНИМ ССЫЛКАМ -->
<script>
  document.addEventListener('click', function(e) {
    var link = e.target.closest('a');
    if (link && link.hostname !== window.location.hostname) {
      gtag('event', 'click', {
        'event_category': 'outbound',
        'event_label': link.href,
        'transport_type': 'beacon'
      });
    }
  });
</script>

<!-- 8. ОТСЛЕЖИВАНИЕ ЗАГРУЗКИ ФАЙЛОВ -->
<script>
  function trackFileDownload(fileName, fileType) {
    gtag('event', 'file_download', {
      'send_to': 'AW-XXXXXXXXXX/DOWNLOAD_LABEL',
      'value': 15.0,
      'currency': 'USD',
      'custom_parameters': {
        'file_name': fileName,
        'file_type': fileType
      }
    });
  }
</script>

<!-- Пример ссылки на скачивание прайса -->
<a href="/price-list.pdf" onclick="trackFileDownload('price-list.pdf', 'pdf')">
  Скачать прайс-лист
</a>

</body>
</html>

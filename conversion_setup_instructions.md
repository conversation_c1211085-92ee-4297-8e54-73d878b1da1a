# Инструкция по внедрению отслеживания конверсий на admins.today

## 🎯 ШАГ 1: Настройка конверсий в Google Ads

### 1.1 Создание конверсий в Google Ads
1. Войти в Google Ads (ads.google.com)
2. **Инструменты** → **Измерения** → **Конверсии**
3. Нажать **"+"** → **Веб-сайт**

### 1.2 Создать следующие конверсии:

#### Конверсия 1: Заявка через форму (ОСНОВНАЯ)
- **Название:** Заявка через форму
- **Категория:** Потенциальный клиент
- **Ценность:** $100
- **Подсчет:** Одна
- **Окно конверсии:** 30 дней
- **Модель атрибуции:** На основе данных

#### Конверсия 2: Звонок с сайта
- **Название:** Звонок с сайта
- **Категория:** Потенциальный клиент
- **Ценность:** $80
- **Подсчет:** Одна

#### Конверсия 3: Выбор тарифа
- **Название:** Выбор тарифа
- **Категория:** Потенциальный клиент
- **Ценность:** $50
- **Подсчет:** Одна

#### Конверсия 4: Переход на контакты
- **Название:** Переход на контакты
- **Категория:** Потенциальный клиент
- **Ценность:** $20
- **Подсчет:** Одна

#### Конверсия 5: Успешная оплата
- **Название:** Успешная оплата
- **Категория:** Покупка
- **Ценность:** Использовать разные значения
- **Подсчет:** Каждая

---

## 🔧 ШАГ 2: Получение кодов отслеживания

После создания каждой конверсии Google Ads выдаст:
1. **Conversion ID** (например: AW-123456789)
2. **Conversion Label** (например: AbC-D_efGhIjKlMnOp)

### Пример полученных данных:
```
Conversion ID: AW-987654321
Заявка через форму: AbCdEfGhIj
Звонок с сайта: KlMnOpQrSt
Выбор тарифа: UvWxYzAbCd
Переход на контакты: EfGhIjKlMn
Успешная оплата: OpQrStUvWx
```

---

## 💻 ШАГ 3: Внедрение кода на сайт

### 3.1 Добавить в `<head>` всех страниц:
```html
<script async src="https://www.googletagmanager.com/gtag/js?id=AW-987654321"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'AW-987654321');
</script>
```

### 3.2 Добавить функции отслеживания:
```html
<script>
  function gtagSendEvent(url) {
    var callback = function () {
      if (typeof url === 'string') {
        window.location = url;
      }
    };
    gtag('event', 'conversion_event_purchase', {
      'send_to': 'AW-987654321/OpQrStUvWx',
      'event_callback': callback,
      'event_timeout': 2000,
      'value': 100.0,
      'currency': 'USD'
    });
    return false;
  }

  function trackFormSubmission() {
    gtag('event', 'conversion', {
      'send_to': 'AW-987654321/AbCdEfGhIj',
      'value': 100.0,
      'currency': 'USD'
    });
  }

  function trackPhoneClick() {
    gtag('event', 'conversion', {
      'send_to': 'AW-987654321/KlMnOpQrSt',
      'value': 80.0,
      'currency': 'USD'
    });
  }
</script>
```

---

## 🎨 ШАГ 4: Модификация HTML элементов

### 4.1 Форма заказа услуг
**НАЙТИ:**
```html
<form>
  <!-- поля формы -->
  <button type="submit">Оплатити і замовити</button>
</form>
```

**ЗАМЕНИТЬ НА:**
```html
<form onsubmit="trackFormSubmission(); return true;">
  <!-- поля формы -->
  <button type="submit">Оплатити і замовити</button>
</form>
```

### 4.2 Кнопки тарифов
**НАЙТИ:**
```html
<button>Базовий - $20/місяць</button>
<button>Стандарт - $50/місяць</button>
<button>Преміум - $100/місяць</button>
<button>Корпоративний - $200/місяць</button>
```

**ЗАМЕНИТЬ НА:**
```html
<button onclick="gtag('event', 'conversion', {'send_to': 'AW-987654321/UvWxYzAbCd', 'value': 20, 'currency': 'USD'})">Базовий - $20/місяць</button>
<button onclick="gtag('event', 'conversion', {'send_to': 'AW-987654321/UvWxYzAbCd', 'value': 50, 'currency': 'USD'})">Стандарт - $50/місяць</button>
<button onclick="gtag('event', 'conversion', {'send_to': 'AW-987654321/UvWxYzAbCd', 'value': 100, 'currency': 'USD'})">Преміум - $100/місяць</button>
<button onclick="gtag('event', 'conversion', {'send_to': 'AW-987654321/UvWxYzAbCd', 'value': 200, 'currency': 'USD'})">Корпоративний - $200/місяць</button>
```

### 4.3 Ссылка на контакты
**НАЙТИ:**
```html
<a href="/contacts">Контакти</a>
```

**ЗАМЕНИТЬ НА:**
```html
<a href="/contacts" onclick="gtag('event', 'conversion', {'send_to': 'AW-987654321/EfGhIjKlMn', 'value': 20, 'currency': 'USD'})">Контакти</a>
```

### 4.4 Кнопка "Замовити послуги"
**НАЙТИ:**
```html
<button>Замовити послуги</button>
```

**ЗАМЕНИТЬ НА:**
```html
<button onclick="gtagSendEvent('/#order')">Замовити послуги</button>
```

---

## 🧪 ШАГ 5: Тестирование

### 5.1 Проверка установки кода
1. Открыть сайт в браузере
2. Нажать F12 → Console
3. Ввести: `gtag('event', 'test')`
4. Если ошибок нет - код установлен правильно

### 5.2 Тестирование конверсий
1. В Google Ads: **Инструменты** → **Конверсии**
2. Найти созданные конверсии
3. Проверить статус: должен быть "Недавно активна"
4. Выполнить тестовые действия на сайте
5. Через 1-2 часа проверить появление конверсий

### 5.3 Использование Google Tag Assistant
1. Установить расширение Google Tag Assistant
2. Открыть сайт
3. Проверить корректность работы тегов

---

## 📊 ШАГ 6: Настройка Enhanced Conversions

### 6.1 Включить Enhanced Conversions
1. В Google Ads: **Инструменты** → **Конверсии**
2. Выбрать конверсию "Заявка через форму"
3. **Настройки** → **Улучшенные конверсии**
4. Включить и выбрать "Google Tag"

### 6.2 Добавить код для Enhanced Conversions
```html
<script>
  function submitFormWithEnhancedData() {
    var email = document.querySelector('input[name="email"]').value;
    var name = document.querySelector('input[name="name"]').value;
    
    gtag('config', 'AW-987654321', {
      'user_data': {
        'email_address': email,
        'first_name': name.split(' ')[0] || ''
      }
    });
    
    trackFormSubmission();
  }
</script>
```

---

## ⚠️ ВАЖНЫЕ МОМЕНТЫ

### Замена ID и лейблов
Во всех примерах кода заменить:
- `AW-987654321` → ваш реальный Conversion ID
- `AbCdEfGhIj` → лейбл "Заявка через форму"
- `KlMnOpQrSt` → лейбл "Звонок с сайта"
- `UvWxYzAbCd` → лейбл "Выбор тарифа"
- `EfGhIjKlMn` → лейбл "Переход на контакты"
- `OpQrStUvWx` → лейбл "Успешная оплата"

### Проверка работы
- Конверсии появляются в отчетах через 2-3 часа
- Для тестирования используйте режим инкогнито
- Проверяйте консоль браузера на ошибки

### Соответствие GDPR
Добавить уведомление о cookies:
```html
<div id="cookie-notice">
  Цей сайт використовує cookies для аналітики та реклами.
  <button onclick="acceptCookies()">Прийняти</button>
</div>
```

---

## 🎯 РЕЗУЛЬТАТ

После внедрения вы получите:
✅ Отслеживание всех ключевых действий пользователей  
✅ Данные для оптимизации рекламных кампаний  
✅ Возможность настройки автоматических стратегий ставок  
✅ Ремаркетинг на основе действий пользователей  
✅ Точную атрибуцию конверсий к рекламным кампаниям

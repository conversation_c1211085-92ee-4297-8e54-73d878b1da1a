<!-- ИНТЕГРАЦИЯ ОТСЛЕЖИВАНИЯ КОНВЕРСИЙ В СУЩЕСТВУЮЩИЙ КОД ADMINS.TODAY -->

<!-- 1. ДОБАВИТЬ В <head> ВСЕХ СТРАНИЦ -->
<script async src="https://www.googletagmanager.com/gtag/js?id=AW-XXXXXXXXXX"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'AW-XXXXXXXXXX'); // Заменить на ваш Google Ads ID
</script>

<!-- 2. ФУНКЦИЯ ДЛЯ ОТСЛЕЖИВАНИЯ КОНВЕРСИЙ С ЗАДЕРЖКОЙ НАВИГАЦИИ -->
<script>
  // Ваша функция с delayed navigation helper
  function gtagSendEvent(url) {
    var callback = function () {
      if (typeof url === 'string') {
        window.location = url;
      }
    };
    gtag('event', 'conversion_event_purchase', {
      'send_to': 'AW-XXXXXXXXXX/PURCHASE_LABEL', // Заменить на ваш лейбл
      'event_callback': callback,
      'event_timeout': 2000,
      'value': 100.0,
      'currency': 'USD'
    });
    return false;
  }

  // Дополнительные функции для разных типов конверсий
  function trackFormSubmission() {
    gtag('event', 'conversion', {
      'send_to': 'AW-XXXXXXXXXX/FORM_LABEL',
      'value': 100.0,
      'currency': 'USD'
    });
  }

  function trackPhoneClick() {
    gtag('event', 'conversion', {
      'send_to': 'AW-XXXXXXXXXX/PHONE_LABEL',
      'value': 80.0,
      'currency': 'USD'
    });
  }

  function trackPricingClick(planName, price) {
    gtag('event', 'conversion', {
      'send_to': 'AW-XXXXXXXXXX/PRICING_LABEL',
      'value': price,
      'currency': 'USD',
      'custom_parameters': {
        'plan': planName
      }
    });
  }
</script>

<!-- 3. МОДИФИКАЦИЯ СУЩЕСТВУЮЩЕЙ ФОРМЫ ЗАКАЗА -->
<!-- БЫЛО: -->
<!--
<form>
  <input type="text" name="name" placeholder="Ім'я" required>
  <input type="email" name="email" placeholder="Email" required>
  <select name="service">
    <option value="">Оберіть послугу</option>
    <option value="techsupport">Техпідтримка</option>
    <option value="sysadmin">Системне адміністрування</option>
    <option value="security">Безпека</option>
    <option value="cloud">Хмарні рішення</option>
    <option value="other">Інше</option>
  </select>
  <textarea name="description" placeholder="Опис завдання" required></textarea>
  <button type="submit">Оплатити і замовити</button>
</form>
-->

<!-- СТАЛО: -->
<form onsubmit="trackFormSubmission(); return true;">
  <input type="text" name="name" placeholder="Ім'я" required>
  <input type="email" name="email" placeholder="Email" required>
  <select name="service">
    <option value="">Оберіть послугу</option>
    <option value="techsupport">Техпідтримка</option>
    <option value="sysadmin">Системне адміністрування</option>
    <option value="security">Безпека</option>
    <option value="cloud">Хмарні рішення</option>
    <option value="other">Інше</option>
  </select>
  <textarea name="description" placeholder="Опис завдання" required></textarea>
  <button type="submit" onclick="trackFormSubmission()">Оплатити і замовити</button>
</form>

<!-- 4. МОДИФИКАЦИЯ КНОПОК ТАРИФОВ -->
<!-- БЫЛО: -->
<!--
<button>Базовий - $20/місяць</button>
<button>Стандарт - $50/місяць</button>
<button>Преміум - $100/місяць</button>
<button>Корпоративний - $200/місяць</button>
-->

<!-- СТАЛО: -->
<button onclick="trackPricingClick('Базовий', 20)">Базовий - $20/місяць</button>
<button onclick="trackPricingClick('Стандарт', 50)">Стандарт - $50/місяць</button>
<button onclick="trackPricingClick('Преміум', 100)">Преміум - $100/місяць</button>
<button onclick="trackPricingClick('Корпоративний', 200)">Корпоративний - $200/місяць</button>

<!-- 5. МОДИФИКАЦИЯ ССЫЛОК В НАВИГАЦИИ -->
<!-- БЫЛО: -->
<!--
<a href="/">Головна</a>
<a href="/#services">Послуги</a>
<a href="/#order">Замовити</a>
<a href="/contacts">Контакти</a>
-->

<!-- СТАЛО: -->
<a href="/">Головна</a>
<a href="/#services">Послуги</a>
<a href="/#order" onclick="gtag('event', 'click', {'event_category': 'navigation', 'event_label': 'order_section'})">Замовити</a>
<a href="/contacts" onclick="gtag('event', 'conversion', {'send_to': 'AW-XXXXXXXXXX/CONTACT_LABEL', 'value': 20.0, 'currency': 'USD'})">Контакти</a>

<!-- 6. МОДИФИКАЦИЯ КНОПОК ДЕЙСТВИЙ -->
<!-- БЫЛО: -->
<!--
<button>Замовити послуги</button>
<button>Дізнатися більше</button>
-->

<!-- СТАЛО: -->
<button onclick="gtagSendEvent('/#order')">Замовити послуги</button>
<button onclick="gtag('event', 'click', {'event_category': 'engagement', 'event_label': 'learn_more'})">Дізнатися більше</button>

<!-- 7. ОТСЛЕЖИВАНИЕ КЛИКОВ ПО ССЫЛКАМ УСЛУГ -->
<!-- БЫЛО: -->
<!--
<a href="techsupport">Детальніше</a>
<a href="sysadmin">Детальніше</a>
<a href="cloud">Детальніше</a>
<a href="security">Детальніше</a>
-->

<!-- СТАЛО: -->
<a href="techsupport" onclick="gtag('event', 'click', {'event_category': 'services', 'event_label': 'techsupport'})">Детальніше</a>
<a href="sysadmin" onclick="gtag('event', 'click', {'event_category': 'services', 'event_label': 'sysadmin'})">Детальніше</a>
<a href="cloud" onclick="gtag('event', 'click', {'event_category': 'services', 'event_label': 'cloud'})">Детальніше</a>
<a href="security" onclick="gtag('event', 'click', {'event_category': 'services', 'event_label': 'security'})">Детальніше</a>

<!-- 8. ОТСЛЕЖИВАНИЕ ВЗАИМОДЕЙСТВИЯ С ЧАТБОТОМ -->
<script>
  // Если есть чатбот помощник
  function trackChatInteraction() {
    gtag('event', 'conversion', {
      'send_to': 'AW-XXXXXXXXXX/CHAT_LABEL',
      'value': 30.0,
      'currency': 'USD'
    });
  }
</script>

<!-- 9. ОТСЛЕЖИВАНИЕ ВРЕМЕНИ НА САЙТЕ (МИКРОКОНВЕРСИЯ) -->
<script>
  // Отслеживание времени на сайте больше 2 минут
  setTimeout(function() {
    gtag('event', 'conversion', {
      'send_to': 'AW-XXXXXXXXXX/ENGAGEMENT_LABEL',
      'value': 10.0,
      'currency': 'USD'
    });
  }, 120000); // 2 минуты

  // Отслеживание скролла до конца страницы
  var scrollTracked = false;
  window.addEventListener('scroll', function() {
    if (!scrollTracked && (window.innerHeight + window.scrollY) >= document.body.offsetHeight - 100) {
      scrollTracked = true;
      gtag('event', 'scroll', {
        'event_category': 'engagement',
        'event_label': 'scroll_to_bottom'
      });
    }
  });
</script>

<!-- 10. ИНТЕГРАЦИЯ С WAYFORPAY (если используется) -->
<script>
  // Функция для отслеживания успешной оплаты
  function wayForPaySuccess(response) {
    gtag('event', 'purchase', {
      'send_to': 'AW-XXXXXXXXXX/PURCHASE_LABEL',
      'value': response.amount,
      'currency': response.currency || 'USD',
      'transaction_id': response.orderReference
    });
    
    // Также отправляем событие конверсии
    gtag('event', 'conversion', {
      'send_to': 'AW-XXXXXXXXXX/PAYMENT_SUCCESS_LABEL',
      'value': response.amount,
      'currency': response.currency || 'USD',
      'transaction_id': response.orderReference
    });
  }

  // Функция для отслеживания неудачной оплаты
  function wayForPayFail(response) {
    gtag('event', 'payment_failed', {
      'event_category': 'ecommerce',
      'event_label': response.reason || 'unknown'
    });
  }
</script>

<!-- 11. ENHANCED CONVERSIONS (улучшенные конверсии) -->
<script>
  function submitFormWithEnhancedData() {
    var email = document.querySelector('input[name="email"]').value;
    var name = document.querySelector('input[name="name"]').value;
    
    // Настройка enhanced conversions
    gtag('config', 'AW-XXXXXXXXXX', {
      'user_data': {
        'email_address': email,
        'first_name': name.split(' ')[0] || '',
        'last_name': name.split(' ')[1] || ''
      }
    });
    
    // Отправка основной конверсии
    trackFormSubmission();
  }
</script>

<!-- 12. ОТСЛЕЖИВАНИЕ КЛИКОВ ПО ТЕЛЕФОНУ -->
<!-- Если есть ссылка на телефон -->
<a href="tel:+380123456789" onclick="trackPhoneClick()">Зателефонувати</a>

<!-- 13. ОТСЛЕЖИВАНИЕ ЗАГРУЗКИ ФАЙЛОВ -->
<script>
  function trackFileDownload(fileName) {
    gtag('event', 'file_download', {
      'event_category': 'downloads',
      'event_label': fileName,
      'value': 15.0,
      'currency': 'USD'
    });
  }
</script>

<!-- Если есть ссылки на скачивание -->
<a href="/price-list.pdf" onclick="trackFileDownload('price-list.pdf')">Завантажити прайс</a>

<!-- 14. ОТСЛЕЖИВАНИЕ ПРОСМОТРА ВИДЕО (если есть) -->
<script>
  function trackVideoPlay(videoName) {
    gtag('event', 'video_play', {
      'event_category': 'engagement',
      'event_label': videoName
    });
  }

  function trackVideoComplete(videoName) {
    gtag('event', 'video_complete', {
      'event_category': 'engagement',
      'event_label': videoName,
      'value': 25.0,
      'currency': 'USD'
    });
  }
</script>

<!-- 15. ОТСЛЕЖИВАНИЕ СОЦИАЛЬНЫХ СЕТЕЙ -->
<script>
  function trackSocialClick(platform) {
    gtag('event', 'social_click', {
      'event_category': 'social',
      'event_label': platform
    });
  }
</script>

<!-- Если есть ссылки на соцсети -->
<a href="https://facebook.com/adminstoday" onclick="trackSocialClick('facebook')">Facebook</a>
<a href="https://linkedin.com/company/adminstoday" onclick="trackSocialClick('linkedin')">LinkedIn</a>

<!-- ИНСТРУКЦИИ ПО ЗАМЕНЕ ID -->
<!--
ВАЖНО! Заменить во всех местах:
1. AW-XXXXXXXXXX - на ваш реальный Google Ads ID
2. FORM_LABEL - на лейбл конверсии "Заявка через форму"
3. PHONE_LABEL - на лейбл конверсии "Звонок"
4. PRICING_LABEL - на лейбл конверсии "Выбор тарифа"
5. CONTACT_LABEL - на лейбл конверсии "Переход на контакты"
6. PURCHASE_LABEL - на лейбл конверсии "Покупка"
7. ENGAGEMENT_LABEL - на лейбл микроконверсии "Вовлеченность"

Получить эти данные можно в Google Ads:
Инструменты → Измерения → Конверсии → Создать конверсию
-->
